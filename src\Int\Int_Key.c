#include "Int_Key.h"
#include <STC89C5xRC.H>
#include "Com/Com_Util.h"
#define SW1 P42
#define SW2 P43
#define SW3 P32
#define SW4 P33
bit Int_Key_IsSw1Pressed()
{
  if (SW1==0)
      {
         Com_Util_Delay1ms(10);
         if (SW1==0)          
         {
            while (SW1==0)
            {
                  LED1=~LED1;
            }
            
         }
         
      }
}
bit Int_Key_IsSw2Pressed()
{
    return bit();
}
bit Int_Key_IsSw3Pressed()
{
    return bit();
}
bit Int_Key_IsSw4Pressed()
{
    return bit();
}