{"name": "DULIANJIAN", "type": "C51", "dependenceList": [], "srcDirs": ["src"], "outDir": "build", "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": []}, "miscInfo": {"uid": "8976bdf290ad7dbe740e8953cdd3c32b"}, "targets": {"Release": {"excludeList": [], "toolchain": "Keil_C51", "compileConfig": {"options": ""}, "uploader": "Custom", "uploadConfig": {"bin": "", "commandLine": "python ./tools/stcflash.py -p ${port} \"${hexFile}\"", "eraseChipCommand": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../../../KEIL5/C51/INC/STC", "src", "src/Com"], "libList": [], "defineList": []}, "builderOptions": {"Keil_C51": {"version": 2, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"ram-mode": "SMALL", "rom-mode": "LARGE"}, "c/cpp-compiler": {"optimization-type": "SPEED", "optimization-level": "level-8"}, "asm-compiler": {}, "linker": {"remove-unused": true, "output-format": "elf"}}}}}, "version": "3.6", "deviceName": null, "packDir": null}